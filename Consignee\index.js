// consignee.js
document.addEventListener('DOMContentLoaded', () => {
    console.log('Consignee page loaded in tab mode');

    // Consignee specific data and logic
    let consigneeData = [
        { id: 1, location: '177N-177N', type: 'Warehouse', address: '177N-177N-Bangalore, Karnataka, India, 560001', warehouse: '177N-177N', taxId: '29AAAAA0000A1Z5', contactPerson: '<PERSON><PERSON>', contactPhone: '9876543210', contactEmail: '<EMAIL>', specialInstructions: 'Deliver to Gate 3 only.', isDefault: true, isActive: true, selected: false },
        { id: 2, location: '177N-MB01', type: 'Retailer', address: 'Shop 12, MG Road, Mumbai, Maharashtra, India, 400001', warehouse: '177N-MB01', taxId: '27BBBBB0000B1Z2', contactPerson: '<PERSON><PERSON> <PERSON>', contactPhone: '9123456780', contactEmail: '<EMAIL>', specialInstructions: '', isDefault: false, isActive: true, selected: false },
        { id: 3, location: '177N-MB02', type: 'Distributor', address: 'Plot 45, Industrial Area, Delhi, NCR, India, 110001', warehouse: '177N-MB02', taxId: '07CCCCC0000C1Z8', contactPerson: 'Amit Singh', contactPhone: '9988776655', contactEmail: '<EMAIL>', specialInstructions: 'Call before delivery.', isDefault: false, isActive: true, selected: false },
        { id: 4, location: '177N-PROJ', type: 'End Customer', address: 'Project Site Alpha, HiTech City, Hyderabad, Telangana, 500081', warehouse: '177N-PROJ', taxId: '', contactPerson: 'Deepa Rao', contactPhone: '9000011122', contactEmail: '<EMAIL>', specialInstructions: 'Strict timings: 10 AM - 4 PM.', isDefault: false, isActive: true, selected: false },
        { id: 5, location: 'SOUTH-HUB', type: 'Internal Transfer', address: 'Southern Logistics Hub, Chennai, Tamil Nadu, 600002', warehouse: 'South Main WH', taxId: '33DDDDD0000D1Z4', contactPerson: 'Logistics Team', contactPhone: '044-23456789', contactEmail: '<EMAIL>', specialInstructions: 'Internal stock movement.', isDefault: false, isActive: false, selected: false },
    ];

    let currentSort = { column: 'location', order: 'asc' };
    let currentPage = 1;
    let itemsPerPage = parseInt(localStorage.getItem('consigneePageItemsPerPage')) || 7;
    let currentFilter = '';

    const el = (id) => document.getElementById(id);

    const tableBody = el('consignee-table-body');
    const addConsigneeBtn = el('add-consignee-btn');
    const consigneeModal = el('consignee-modal');
    const closeModalBtn = el('close-modal-btn');
    const cancelModalBtn = el('cancel-modal-btn');
    const consigneeForm = el('consignee-form');
    // Use a more specific ID for the modal title if the main page also has 'modal-title'
    const consigneeSpecificModalTitle = consigneeModal ? consigneeModal.querySelector('#modal-title') : null;
    const consigneeIdInput = el('consignee-id');

    const locationInput = el('location');
    const addressInput = el('address');
    const warehouseInput = el('warehouse');

    const selectAllCheckbox = el('select-all-checkbox');
    const deleteSelectedBtn = el('delete-selected-btn');
    const consigneeSearchInput = el('consignee-search-input');
    const refreshBtn = el('refresh-btn');
    const exportBtn = el('export-btn');
    const saveConsigneeBtn = el('save-consignee-btn');

    const suggestAddressBtn = el('suggest-address-btn');
    const addressSuggestionLoader = el('address-suggestion-loader');
    const generateLocationCodeBtn = el('generate-location-code-btn');
    const locationCodeLoader = el('location-code-loader');

    const prevPageBtn = el('prev-page-btn');
    const nextPageBtn = el('next-page-btn');
    const currentPageDisplay = el('current-page-display');
    const currentPageStart = el('current-page-start');
    const currentPageEnd = el('current-page-end');
    const totalItemsDisplay = el('total-items');
    const itemsPerPageSelect = el('items-per-page-select');

    // Consignee specific notification elements
    // Ensure these IDs are unique to the consignee page if it's embedded or shares script.js context
    const consigneeNotificationModal = el('notification-modal');
    const consigneeSpecificNotificationTitle = consigneeNotificationModal ? consigneeNotificationModal.querySelector('#notification-title') : null;
    const consigneeSpecificNotificationMessage = consigneeNotificationModal ? consigneeNotificationModal.querySelector('#notification-message') : null;
    const consigneeSpecificNotificationIconContainer = consigneeNotificationModal ? consigneeNotificationModal.querySelector('#notification-icon-container') : null;
    const consigneeSpecificNotificationLoaderElement = consigneeNotificationModal ? consigneeNotificationModal.querySelector('#notification-loader') : null;
    const consigneeSpecificNotificationButtons = consigneeNotificationModal ? consigneeNotificationModal.querySelector('#notification-buttons') : null;
    const consigneeSpecificNotificationConfirmBtn = consigneeNotificationModal ? consigneeNotificationModal.querySelector('#notification-confirm-btn') : null;
    const consigneeSpecificNotificationCancelBtn = consigneeNotificationModal ? consigneeNotificationModal.querySelector('#notification-cancel-btn') : null;
    let consigneeNotificationConfirmCallback = null;

    const toastContainer = el('toast-container');

    function showGeminiButtonLoader(btn,ldr){ if(btn) btn.classList.add('hidden'); if(ldr) ldr.classList.remove('hidden');}
    function hideGeminiButtonLoader(btn,ldr){ if(btn) btn.classList.remove('hidden'); if(ldr) ldr.classList.add('hidden');}

    function showToast(msg, type='info', dur=3000){
        if (!toastContainer) {console.warn("Toast container not found"); return;}
        const t=document.createElement('div'); t.className=`toast toast-${type}`;
        let iCls='fas fa-info-circle'; if(type==='success')iCls='fas fa-check-circle'; else if(type==='error')iCls='fas fa-times-circle'; else if(type==='warning')iCls='fas fa-exclamation-triangle';
        t.innerHTML=`<i class="${iCls} toast-icon"></i><span>${msg}</span>`; toastContainer.appendChild(t);
        setTimeout(()=>t.classList.add('show'),10); setTimeout(()=>{t.classList.remove('show');setTimeout(()=>t.remove(),300)},dur);
    }

    function showConsigneeNotification({title,message,type='info',onConfirm,confirmText='Confirm',showCancel=true,isLoading=false}){
        if (!consigneeNotificationModal || !consigneeSpecificNotificationTitle || !consigneeSpecificNotificationMessage || !consigneeSpecificNotificationIconContainer || !consigneeSpecificNotificationConfirmBtn || !consigneeSpecificNotificationCancelBtn || !consigneeSpecificNotificationLoaderElement || !consigneeSpecificNotificationButtons) {
            console.warn("One or more consignee notification modal elements not found.");
            return;
        }
        consigneeSpecificNotificationTitle.textContent=title; consigneeSpecificNotificationMessage.textContent=message; consigneeSpecificNotificationIconContainer.innerHTML='';
        let iCls='fas fa-info-circle text-neutral-400'; if(type==='success')iCls='fas fa-check-circle text-green-400'; else if(type==='warning')iCls='fas fa-exclamation-triangle text-yellow-400'; else if(type==='error')iCls='fas fa-times-circle text-red-400'; else if(type==='loading')iCls='fas fa-spinner fa-spin text-neutral-400';
        const iEl=document.createElement('i'); iEl.className=`${iCls} fa-2x`; consigneeSpecificNotificationIconContainer.appendChild(iEl);
        consigneeNotificationConfirmCallback=onConfirm; consigneeSpecificNotificationConfirmBtn.textContent=confirmText;
        if(isLoading||type==='loading'){consigneeSpecificNotificationLoaderElement.classList.remove('hidden');consigneeSpecificNotificationButtons.classList.add('hidden');}
        else{consigneeSpecificNotificationLoaderElement.classList.add('hidden');consigneeSpecificNotificationButtons.classList.remove('hidden');
            consigneeSpecificNotificationConfirmBtn.className=`py-2 px-4 font-medium rounded-lg transition-colors ${ (type==='error'||type==='warning'||confirmText.toLowerCase()==='delete'||confirmText.toLowerCase()==='confirm')&&onConfirm ? 'bg-red-700 hover:bg-red-600 text-white':'bg-neutral-200 hover:bg-neutral-300 text-neutral-800' }`;}
        consigneeSpecificNotificationConfirmBtn.style.display=onConfirm?'inline-flex':'none'; consigneeSpecificNotificationCancelBtn.style.display=showCancel?'inline-flex':'none';
        if(!onConfirm&&!showCancel){consigneeSpecificNotificationCancelBtn.textContent='OK';consigneeSpecificNotificationCancelBtn.style.display='inline-flex';consigneeSpecificNotificationCancelBtn.className='py-2 px-4 bg-neutral-200 hover:bg-neutral-300 text-neutral-800 font-medium rounded-lg';}
        else{consigneeSpecificNotificationCancelBtn.textContent='Cancel';consigneeSpecificNotificationCancelBtn.className='py-2 px-4 bg-neutral-600 hover:bg-neutral-500 text-neutral-200 font-medium rounded-lg';}
        consigneeNotificationModal.classList.remove('hidden'); setTimeout(()=>consigneeNotificationModal.classList.add('modal-enter-active'),10);
    }
    function hideConsigneeNotification(){if (!consigneeNotificationModal) return; consigneeNotificationModal.classList.remove('modal-enter-active');setTimeout(()=>consigneeNotificationModal.classList.add('hidden'),200);consigneeNotificationConfirmCallback=null;}
    if(consigneeSpecificNotificationConfirmBtn) consigneeSpecificNotificationConfirmBtn.onclick=()=>{if(consigneeNotificationConfirmCallback)consigneeNotificationConfirmCallback();if(consigneeSpecificNotificationLoaderElement && !consigneeSpecificNotificationLoaderElement.classList.contains('hidden'))return;hideConsigneeNotification();};
    if(consigneeSpecificNotificationCancelBtn) consigneeSpecificNotificationCancelBtn.onclick=hideConsigneeNotification;

    async function callGeminiAPI(prompt){const key=""; const url=`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${key}`;
        const pLoad={contents:[{role:"user",parts:[{text:prompt}]}]};
        try{const res=await fetch(url,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(pLoad)});
            if(!res.ok){const errD=await res.json();throw new Error(`API fail: ${errD?.error?.message||res.statusText}`);}
            const r=await res.json(); if(r.candidates?.[0]?.content?.parts?.[0]?.text)return r.candidates[0].content.parts[0].text.trim();
            throw new Error('No valid content from Gemini.');
        }catch(e){console.error('Gemini call error:',e);throw e;}}

    if(suggestAddressBtn) suggestAddressBtn.onclick=async()=>{const curAddr=addressInput.value.trim();if(!curAddr){showToast('Enter address.','warning');return;}
        showGeminiButtonLoader(suggestAddressBtn,addressSuggestionLoader);try{
        const prmpt=`Correct, complete, standardize address. Return only corrected: "${curAddr}"`;addressInput.value=await callGeminiAPI(prmpt);showToast('Address suggested!','success');
        }catch(e){showToast(`Addr suggest fail: ${e.message}`,'error');}finally{hideGeminiButtonLoader(suggestAddressBtn,addressSuggestionLoader);}};

    if(generateLocationCodeBtn) generateLocationCodeBtn.onclick=async()=>{const addr=addressInput.value.trim(),wh=warehouseInput.value.trim();if(!addr&&!wh){showToast('Need address/warehouse.','warning');return;}
        showGeminiButtonLoader(generateLocationCodeBtn,locationCodeLoader);try{
        const prmpt=`Suggest concise, unique location code (e.g. BLR-MAIN) from addr: "${addr}" and warehouse: "${wh}". Only code.`;locationInput.value=await callGeminiAPI(prmpt);showToast('Code generated!','success');
        }catch(e){showToast(`Code gen fail: ${e.message}`,'error');}finally{hideGeminiButtonLoader(generateLocationCodeBtn,locationCodeLoader);}};

    function renderTable(){
        if(!tableBody) { console.warn("Table body not found for rendering."); return; }
        tableBody.innerHTML='';
        const filtData=consigneeData.filter(i=>{const sTerm=currentFilter.toLowerCase();if(!sTerm)return true;return Object.values(i).some(v=>String(v).toLowerCase().includes(sTerm));});
        const sortData=[...filtData].sort((a,b)=>{let vA=a[currentSort.column],vB=b[currentSort.column];
            if(typeof vA==='boolean'){vA=vA?1:0;vB=vB?1:0;}else{vA=String(vA??'').toLowerCase();vB=String(vB??'').toLowerCase();}
            if(vA<vB)return currentSort.order==='asc'?-1:1;if(vA>vB)return currentSort.order==='asc'?1:-1;return 0;});
        const startIdx=(currentPage-1)*itemsPerPage; const pagData=sortData.slice(startIdx,startIdx+itemsPerPage);
        if(pagData.length===0&&filtData.length>0){currentPage=Math.max(1,Math.ceil(filtData.length/itemsPerPage));renderTable();return;}
        if(pagData.length===0){tableBody.innerHTML=`<tr><td colspan="10" class="px-6 py-12 text-center text-neutral-500"><div class="flex flex-col items-center"><i class="fas fa-ghost fa-3x mb-3 text-neutral-600"></i><span class="font-semibold">No consignees.</span>${currentFilter?'<p class="text-sm">Adjust search.</p>':'<p class="text-sm">Add new.</p>'}</div></td></tr>`;}
        else{pagData.forEach(item=>{const tr=document.createElement('tr');
            let rCls='hover:bg-neutral-700 transition-colors duration-150';if(item.selected)rCls+=' row-selected';if(item.isDefault)rCls+=' default-consignee-row'; tr.className=rCls; tr.dataset.id=item.id;
            tr.innerHTML=`
                <td class="p-4 relative">${item.isDefault?'<i class="fas fa-star absolute top-1 left-1 text-xs" title="Default"></i>':''}<input type="checkbox" class="custom-checkbox row-checkbox ml-3" data-id="${item.id}" ${item.selected?'checked':''}></td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-neutral-300">${item.location}</td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-neutral-400">${item.type || 'N/A'}</td>
                <td class="px-4 py-3 text-sm text-neutral-400 max-w-[200px] truncate" title="${item.address}">${item.address}</td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-neutral-400">${item.warehouse}</td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-neutral-400">${item.taxId || 'N/A'}</td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-neutral-400"><div class="truncate max-w-[150px]" title="${item.contactPerson} (${item.contactPhone || 'N/A'})">${item.contactPerson || 'N/A'}<br><span class="text-xs text-neutral-500">${item.contactPhone || ''}</span></div></td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-center"><span class="${item.isDefault?'text-neutral-200 font-bold':'text-neutral-500'}">${item.isDefault?'Yes':'No'}</span></td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-center"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.isActive?'bg-green-700 text-green-100':'bg-red-700 text-red-100'}">${item.isActive?'Active':'Inactive'}</span></td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-center font-medium">
                    <button class="edit-btn text-neutral-400 hover:text-neutral-200 mr-3" data-id="${item.id}" title="Edit"><i class="fas fa-pencil-alt"></i></button>
                    <button class="delete-btn text-red-500 hover:text-red-400" data-id="${item.id}" title="Delete"><i class="fas fa-trash-alt"></i></button>
                </td>`; tableBody.appendChild(tr);});}
        updatePagination(filtData.length); updateSelectAllCheckboxState(); updateSortIndicators();
    }
    document.querySelectorAll('.sortable-header').forEach(h=>h.onclick=()=>{const k=h.dataset.sortKey;if(currentSort.column===k)currentSort.order=currentSort.order==='asc'?'desc':'asc';else{currentSort.column=k;currentSort.order='asc';}renderTable();});
    function updateSortIndicators(){document.querySelectorAll('.sortable-header .sort-indicator').forEach(i=>i.innerHTML='');const actH=document.querySelector(`.sortable-header[data-sort-key="${currentSort.column}"] .sort-indicator`);if(actH)actH.innerHTML=currentSort.order==='asc'?'<i class="fas fa-arrow-up"></i>':'<i class="fas fa-arrow-down"></i>';}
    function updatePagination(total){
        if(!currentPageDisplay || !totalItemsDisplay || !currentPageStart || !currentPageEnd || !prevPageBtn || !nextPageBtn) return;
        const totPg=Math.ceil(total/itemsPerPage);currentPage=Math.max(1,Math.min(currentPage,totPg===0?1:totPg));currentPageDisplay.textContent=totPg===0?0:currentPage;totalItemsDisplay.textContent=total;
        const st=total===0?0:(currentPage-1)*itemsPerPage+1;currentPageStart.textContent=st;currentPageEnd.textContent=Math.min(currentPage*itemsPerPage,total);
        prevPageBtn.disabled=currentPage===1;nextPageBtn.disabled=currentPage===totPg||totPg===0;}

    if(itemsPerPageSelect) {
      itemsPerPageSelect.value=itemsPerPage;
      itemsPerPageSelect.onchange=e=>{itemsPerPage=parseInt(e.target.value);localStorage.setItem('consigneePageItemsPerPage',itemsPerPage);currentPage=1;renderTable();};
    }

    function openModal(data=null){
        if(!consigneeForm || !consigneeModal || !consigneeSpecificModalTitle) return;
        consigneeForm.reset();
        if(suggestAddressBtn && addressSuggestionLoader) hideGeminiButtonLoader(suggestAddressBtn,addressSuggestionLoader);
        if(generateLocationCodeBtn && locationCodeLoader) hideGeminiButtonLoader(generateLocationCodeBtn,locationCodeLoader);

        if(data){consigneeSpecificModalTitle.textContent='Edit Consignee';Object.keys(data).forEach(key=>{const el=consigneeForm.elements[key];if(el){if(el.type==='checkbox')el.checked=data[key];else el.value=data[key];}});if(consigneeIdInput) consigneeIdInput.value=data.id;}
        else{consigneeSpecificModalTitle.textContent='Add New Consignee';if(consigneeIdInput) consigneeIdInput.value='';const isActiveEl = el('isActive'); if(isActiveEl) isActiveEl.checked=true;}
        consigneeModal.classList.remove('hidden');setTimeout(()=>consigneeModal.classList.add('modal-enter-active'),10);}

    function closeModal(){if(!consigneeModal) return; consigneeModal.classList.remove('modal-enter-active');setTimeout(()=>consigneeModal.classList.add('hidden'),200);}

    if(consigneeForm && saveConsigneeBtn) consigneeForm.onsubmit=e=>{e.preventDefault();let valid=true;consigneeForm.querySelectorAll('[required]').forEach(inp=>{if(!inp.value.trim()){inp.classList.add('border-red-500');valid=false;}else{inp.classList.remove('border-red-500');}});if(!valid){showToast('Fill required fields.','warning');return;}
        saveConsigneeBtn.disabled=true;saveConsigneeBtn.innerHTML='<div class="spinner"></div> Saving...';const id=consigneeIdInput.value;
        const frmData=new FormData(consigneeForm);const newCon={id:id?parseInt(id):Date.now(),selected:false};
        for(let[key,value]of frmData.entries()){const elem=consigneeForm.elements[key];if(elem){if(elem.type==='checkbox')newCon[key]=elem.checked;else newCon[key]=value;}}
        if(newCon.isDefault)consigneeData.forEach(i=>{if(i.id!==newCon.id)i.isDefault=false;});
        setTimeout(()=>{if(id)consigneeData=consigneeData.map(i=>i.id===newCon.id?newCon:i);else consigneeData.unshift(newCon);
        closeModal();renderTable();saveConsigneeBtn.disabled=false;saveConsigneeBtn.innerHTML='Save Consignee';showToast(`Consignee ${id?'updated':'added'}.`,'success');},500);};

    if(tableBody) tableBody.onclick=e=>{const tgt=e.target.closest('button, input[type="checkbox"].row-checkbox');if(!tgt)return;const id=parseInt(tgt.dataset.id||tgt.closest('tr')?.dataset.id);const con=consigneeData.find(i=>i.id===id);
        if(tgt.classList.contains('edit-btn')&&con)openModal(con);
        else if(tgt.classList.contains('delete-btn')&&con){showConsigneeNotification({title:'Delete Consignee',type:'warning',message:`Delete ${con.location}? Cannot undo.`,onConfirm:()=>{consigneeData=consigneeData.filter(i=>i.id!==id);renderTable();showToast('Consignee removed.','success');}});}
        else if(tgt.classList.contains('row-checkbox')&&con){con.selected=tgt.checked;tgt.closest('tr').classList.toggle('row-selected',con.selected);updateSelectAllCheckboxState();}};

    if(selectAllCheckbox) selectAllCheckbox.onchange=e=>{const chkd=e.target.checked;const filtData=consigneeData.filter(i=>{const sTerm=currentFilter.toLowerCase();return Object.values(i).some(v=>String(v).toLowerCase().includes(sTerm));});
        const pagIds=filtData.slice((currentPage-1)*itemsPerPage,currentPage*itemsPerPage).map(i=>i.id);consigneeData.forEach(i=>{if(pagIds.includes(i.id))i.selected=chkd;});renderTable();};

    function updateSelectAllCheckboxState(){
        if(!selectAllCheckbox) return;
        const fD=consigneeData.filter(i=>String(Object.values(i)).toLowerCase().includes(currentFilter.toLowerCase()));const pI=fD.slice((currentPage-1)*itemsPerPage,currentPage*itemsPerPage);
        if(pI.length>0){selectAllCheckbox.checked=pI.every(i=>i.selected);selectAllCheckbox.indeterminate=!selectAllCheckbox.checked&&pI.some(i=>i.selected);}
        else{selectAllCheckbox.checked=false;selectAllCheckbox.indeterminate=false;}}

    if(deleteSelectedBtn) deleteSelectedBtn.onclick=()=>{const sel=consigneeData.filter(i=>i.selected);if(sel.length===0){showToast('Select items.','info');return;}
        showConsigneeNotification({title:`Delete ${sel.length} Consignee(s)`,type:'warning',message:`Delete ${sel.length} selected? Cannot undo.`,onConfirm:()=>{consigneeData=consigneeData.filter(i=>!i.selected);selectAllCheckbox.checked=false;renderTable();showToast(`${sel.length} removed.`,'success');}});};

    if(consigneeSearchInput) consigneeSearchInput.oninput=e=>{currentFilter=e.target.value;currentPage=1;renderTable();};

    if(refreshBtn) refreshBtn.onclick=()=>{currentFilter='';if(consigneeSearchInput) consigneeSearchInput.value='';currentPage=1;consigneeData.forEach(i=>i.selected=false);renderTable();showToast('Data refreshed.','info');};

    if(exportBtn) exportBtn.onclick=()=>{const expD=consigneeData.filter(i=>i.selected).length>0?consigneeData.filter(i=>i.selected):consigneeData;if(expD.length===0){showToast('No data.','info');return;}
        const json=JSON.stringify(expD.map(({selected,...r})=>r),null,2);const blob=new Blob([json],{type:'application/json'});const url=URL.createObjectURL(blob);
        const a=document.createElement('a');a.href=url;a.download='consignee_data.json';document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);showToast(`${expD.length} records for download.`,'success');};

    if(prevPageBtn) prevPageBtn.onclick=()=>{if(currentPage>1){currentPage--;renderTable();}};
    if(nextPageBtn) nextPageBtn.onclick=()=>{const fD=consigneeData.filter(i=>String(Object.values(i)).toLowerCase().includes(currentFilter.toLowerCase()));if(currentPage<Math.ceil(fD.length/itemsPerPage)){currentPage++;renderTable();}};

    if(addConsigneeBtn) addConsigneeBtn.onclick=()=>openModal();
    if(closeModalBtn) closeModalBtn.onclick=closeModal;
    if(cancelModalBtn) cancelModalBtn.onclick=closeModal;

    // Initial setup
    // initializeBranchDropdown(); // Branch dropdown is now part of the main dashboard header
    renderTable();
});
